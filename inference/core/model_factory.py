# Copyright 2024 onwards Answer.AI, LightOn, and contributors
# License: Apache-2.0

import os
import sys
from pathlib import Path
from typing import Dict, Any, Optional, Tuple
import torch
import torch.nn as nn
import logging

# Add project root to path for imports
sys.path.append(str(Path(__file__).parent.parent.parent))

from src.bert_layers.configuration_bert import FlexBertConfig
from src.bert_layers.model import FlexBertForMaskedLM

# Handle imports - try relative first, then absolute
try:
    from ..config.model_config import ModelConfig
    from ..config.inference_config import InferenceConfig
    from .checkpoint_manager import CheckpointManager
    from ..utils.device_utils import device_manager
    from ..utils.flash_attention_utils import flash_attention_manager
    from ..utils.error_handling import device_error_handler
except ImportError:
    # If relative imports fail, try direct module imports
    inference_dir = Path(__file__).parent.parent
    sys.path.insert(0, str(inference_dir))

    from config.model_config import ModelConfig
    from config.inference_config import InferenceConfig
    from core.checkpoint_manager import CheckpointManager
    from utils.device_utils import device_manager
    from utils.flash_attention_utils import flash_attention_manager
    from utils.error_handling import device_error_handler

logger = logging.getLogger(__name__)


class ModelFactory:
    """
    Factory class for creating and configuring FlexBERT models for inference.
    
    Handles:
    - Model instantiation from configuration
    - Checkpoint loading and validation
    - Device and precision management
    - Configuration compatibility checks
    """
    
    def __init__(self, model_config: ModelConfig, inference_config: InferenceConfig):
        """
        Initialize ModelFactory.

        Args:
            model_config: Model configuration from training YAML
            inference_config: Inference-specific configuration
        """
        self.model_config = model_config
        self.inference_config = inference_config
        self.device = self._setup_device()

        logger.info(f"ModelFactory initialized for device: {self.device}")

        # Log device capabilities
        if self.inference_config.device_info:
            device_info = self.inference_config.device_info
            logger.info(f"Device capabilities: Flash Attention={device_info.supports_flash_attention}, "
                       f"Mixed Precision={device_info.supports_mixed_precision}")

    def _setup_device(self) -> torch.device:
        """Setup and validate the target device using enhanced device detection."""
        # Use the device already selected by InferenceConfig
        if self.inference_config.selected_device is not None:
            device = self.inference_config.selected_device
            logger.debug(f"Using device from inference config: {device}")
        else:
            # Fallback to basic device setup if enhanced detection failed
            logger.warning("Enhanced device detection not available, using fallback")
            device_str = self.inference_config.device

            if device_str == "auto":
                if torch.cuda.is_available():
                    device = torch.device("cuda")
                    logger.info(f"Fallback: Auto-selected CUDA device")
                elif hasattr(torch.backends, 'mps') and torch.backends.mps.is_available():
                    device = torch.device("mps")
                    logger.info(f"Fallback: Auto-selected MPS device")
                else:
                    device = torch.device("cpu")
                    logger.info("Fallback: Auto-selected CPU device")
            else:
                device = torch.device(device_str)
                logger.info(f"Fallback: Using specified device: {device}")

        # Validate device is actually available
        self._validate_device_availability(device)

        return device

    def _validate_device_availability(self, device: torch.device):
        """Validate that the device is actually available and accessible."""
        try:
            if device.type == "cuda":
                if not torch.cuda.is_available():
                    raise RuntimeError("CUDA device requested but CUDA is not available")

                # Test device accessibility
                torch.cuda.set_device(device)
                test_tensor = torch.tensor([1.0], device=device)
                logger.debug(f"CUDA device {device} validated successfully")

            elif device.type == "mps":
                if not (hasattr(torch.backends, 'mps') and torch.backends.mps.is_available()):
                    raise RuntimeError("MPS device requested but MPS is not available")

                # Test MPS accessibility
                test_tensor = torch.tensor([1.0], device=device)
                logger.debug(f"MPS device validated successfully")

            elif device.type == "cpu":
                # CPU is always available
                test_tensor = torch.tensor([1.0], device=device)
                logger.debug(f"CPU device validated successfully")

            else:
                logger.warning(f"Unknown device type: {device.type}")

        except Exception as e:
            # Use error handler for comprehensive error reporting
            error_info = device_error_handler.handle_device_error(e, device, "device validation")
            device_error_handler.log_error_info(error_info)

            # Suggest fallback devices
            fallback_suggestions = device_error_handler.suggest_device_fallback(device)
            if fallback_suggestions:
                logger.info("Device fallback suggestions:")
                for suggestion in fallback_suggestions:
                    logger.info(f"  - {suggestion}")

            raise RuntimeError(f"Cannot use device {device}: {e}")
    
    def create_model(self, checkpoint_path: Optional[str] = None) -> FlexBertForMaskedLM:
        """
        Create and load FlexBERT model for inference.

        Args:
            checkpoint_path: Optional path to model checkpoint

        Returns:
            Loaded FlexBertForMaskedLM model ready for inference
        """
        logger.info("Creating FlexBERT model...")

        # Create model configuration with device-specific optimizations
        bert_config = self._create_bert_config()

        # Instantiate model
        model = FlexBertForMaskedLM(bert_config)

        # Load checkpoint if provided
        if checkpoint_path:
            self._load_checkpoint(model, checkpoint_path)

        # Apply device-specific optimizations before moving to device
        model = self._apply_device_optimizations(model)

        # Move to target device with proper error handling
        model = self._move_to_device(model)

        # Set precision
        model = self._apply_precision(model)

        # Set to evaluation mode
        model.eval()

        # Log final model info
        self._log_model_info(model)

        logger.info(f"Model created successfully on {self.device}")
        return model

    def _apply_device_optimizations(self, model: FlexBertForMaskedLM) -> FlexBertForMaskedLM:
        """Apply device-specific optimizations to the model."""
        device_info = self.inference_config.device_info

        if device_info is None:
            logger.debug("No device info available, skipping device optimizations")
            return model

        # Apply Flash Attention configuration (already done in _create_bert_config)
        # This is a double-check to ensure consistency
        flash_attention_manager.apply_attention_config(model.config, self.device)

        # Device-specific optimizations
        if device_info.device_type == "cuda":
            logger.debug("Applying CUDA-specific optimizations")
            # Enable memory efficient attention if supported
            if hasattr(model.config, 'enable_memory_efficient_attention'):
                model.config.enable_memory_efficient_attention = True

            # CUDA-specific Flash Attention settings
            flash_info = flash_attention_manager.check_device_compatibility(self.device)
            if flash_info.supports_device:
                logger.debug("Flash Attention enabled for CUDA device")
            else:
                logger.info(f"Flash Attention not available on CUDA device: {flash_info.fallback_reason}")

        elif device_info.device_type == "mps":
            logger.debug("Applying MPS-specific optimizations")
            # MPS uses PyTorch SDPA instead of Flash Attention
            logger.info("Using PyTorch SDPA for MPS device (Flash Attention not supported)")

        elif device_info.device_type == "cpu":
            logger.debug("Applying CPU-specific optimizations")
            # CPU uses PyTorch SDPA instead of Flash Attention
            logger.info("Using PyTorch SDPA for CPU device (Flash Attention not supported)")

        return model

    def _move_to_device(self, model: FlexBertForMaskedLM) -> FlexBertForMaskedLM:
        """Move model to target device with proper error handling."""
        try:
            logger.debug(f"Moving model to device: {self.device}")

            # For CUDA, ensure we're using the right device
            if self.device.type == "cuda":
                torch.cuda.set_device(self.device)

            model = model.to(self.device)

            # Verify the move was successful
            sample_param = next(model.parameters())
            if sample_param.device != self.device:
                raise RuntimeError(f"Model move failed: expected {self.device}, got {sample_param.device}")

            logger.debug(f"Model successfully moved to {self.device}")
            return model

        except Exception as e:
            # Use error handler for comprehensive error reporting
            error_info = device_error_handler.handle_device_error(e, self.device, "model device placement")
            device_error_handler.log_error_info(error_info)

            # Try fallback to CPU
            if self.device.type != "cpu":
                logger.warning("Attempting fallback to CPU device")
                try:
                    self.device = torch.device("cpu")
                    model = model.to(self.device)
                    logger.info("Successfully fell back to CPU device")
                    return model
                except Exception as fallback_error:
                    logger.error(f"CPU fallback also failed: {fallback_error}")
                    raise RuntimeError(f"Cannot move model to any device: {e}")
            else:
                raise RuntimeError(f"Cannot move model to any device: {e}")

    def _log_model_info(self, model: FlexBertForMaskedLM):
        """Log detailed model information."""
        try:
            # Count parameters
            total_params = sum(p.numel() for p in model.parameters())
            trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)

            # Get memory usage if on CUDA
            memory_info = ""
            if self.device.type == "cuda":
                memory_info = device_manager.get_device_memory_info(self.device)
                memory_allocated = memory_info.get("allocated", 0) / 1024**3  # GB
                memory_total = memory_info.get("total", 0) / 1024**3  # GB
                memory_info = f", GPU Memory: {memory_allocated:.1f}GB / {memory_total:.1f}GB"

            logger.info(f"Model info: {total_params:,} total params, "
                       f"{trainable_params:,} trainable params{memory_info}")

        except Exception as e:
            logger.debug(f"Could not log detailed model info: {e}")
    
    def _create_bert_config(self) -> FlexBertConfig:
        """Create FlexBertConfig from model configuration with device-specific attention settings."""
        config_dict = self.model_config.get_model_config_dict()

        # Add inference-specific overrides
        config_dict.update({
            'output_hidden_states': self.inference_config.return_hidden_states,
            'use_cache': self.inference_config.use_cache,
        })

        bert_config = FlexBertConfig(**config_dict)

        # Apply device-specific attention configuration
        flash_attention_manager.apply_attention_config(bert_config, self.device)

        # Log attention status
        flash_attention_manager.log_attention_status(self.device)

        logger.debug(f"Created FlexBertConfig with {bert_config.num_hidden_layers} layers")
        return bert_config
    
    def _load_checkpoint(self, model: FlexBertForMaskedLM, checkpoint_path: str):
        """Load model weights from checkpoint."""
        logger.info(f"Loading checkpoint from: {checkpoint_path}")
        
        checkpoint_manager = CheckpointManager(checkpoint_path)
        state_dict, format_type = checkpoint_manager.load_checkpoint()
        
        # Clean up state dict keys if needed
        if any(key.startswith("model.") for key in state_dict.keys()):
            state_dict = checkpoint_manager.clean_state_dict_keys(state_dict, "model.")
            logger.info("Cleaned state dict keys by removing model. prefix")
        logger.info(f"Loaded checkpoint format: {format_type}")
        
        # Load state dict into model
        missing_keys, unexpected_keys = model.load_state_dict(state_dict, strict=False)
        
        if missing_keys:
            logger.warning(f"Missing keys in checkpoint: {missing_keys[:5]}...")
        if unexpected_keys:
            logger.warning(f"Unexpected keys in checkpoint: {unexpected_keys[:5]}...")
        
        logger.info("Checkpoint loaded successfully")
    
    def _apply_precision(self, model: FlexBertForMaskedLM) -> FlexBertForMaskedLM:
        """Apply the specified precision to the model with device-specific handling."""
        precision = self.inference_config.precision
        device_info = self.inference_config.device_info

        try:
            if precision == "fp16":
                model = model.half()
                logger.info(f"Applied FP16 precision on {self.device.type}")

            elif precision == "bf16":
                if self.device.type == "cuda" and torch.cuda.is_bf16_supported():
                    model = model.to(dtype=torch.bfloat16)
                    logger.info(f"Applied BF16 precision on {self.device.type}")
                elif self.device.type == "mps":
                    # MPS has limited BF16 support, fallback to FP16
                    logger.warning("BF16 not well supported on MPS, using FP16")
                    model = model.half()
                else:
                    logger.warning(f"BF16 not supported on {self.device.type}, using FP32")
                    model = model.float()

            elif precision == "fp32":
                model = model.float()
                logger.debug(f"Using FP32 precision on {self.device.type}")

            else:
                logger.warning(f"Unknown precision '{precision}', using FP32")
                model = model.float()

            # Verify precision was applied correctly
            sample_param = next(model.parameters())
            actual_dtype = sample_param.dtype
            logger.debug(f"Model precision: {actual_dtype}")

            # Warn about potential performance implications
            if device_info:
                if not device_info.supports_mixed_precision and precision in ["fp16", "bf16"]:
                    logger.warning(f"Mixed precision may not be optimal on {device_info.device_name}")

            return model

        except Exception as e:
            logger.error(f"Failed to apply precision {precision}: {e}")
            logger.warning("Falling back to FP32 precision")
            return model.float()
    
    def get_model_info(self) -> Dict[str, Any]:
        """
        Get information about the model configuration.
        
        Returns:
            Dictionary with model information
        """
        return {
            "model_name": self.model_config.model_name,
            "hidden_size": self.model_config.hidden_size,
            "num_hidden_layers": self.model_config.num_hidden_layers,
            "num_attention_heads": self.model_config.num_attention_heads,
            "is_unpadded": self.model_config.is_unpadded,
            "attention_layer": self.model_config.attention_layer,
            "device": str(self.device),
            "precision": self.inference_config.precision,
            "parameter_count": self._estimate_parameter_count(),
        }
    
    def _estimate_parameter_count(self) -> int:
        """Estimate the number of parameters in the model."""
        # Rough estimation based on model configuration
        hidden_size = self.model_config.hidden_size
        num_layers = self.model_config.num_hidden_layers
        vocab_size = self.model_config.vocab_size
        intermediate_size = self.model_config.intermediate_size
        
        # Embedding parameters
        embed_params = vocab_size * hidden_size
        
        # Attention parameters per layer
        attn_params_per_layer = (
            4 * hidden_size * hidden_size +  # Q, K, V, O projections
            hidden_size  # Layer norm
        )
        
        # MLP parameters per layer
        mlp_params_per_layer = (
            2 * hidden_size * intermediate_size +  # Up and down projections
            hidden_size  # Layer norm
        )
        
        # Total layer parameters
        layer_params = num_layers * (attn_params_per_layer + mlp_params_per_layer)
        
        # Output head
        head_params = vocab_size * hidden_size
        
        total_params = embed_params + layer_params + head_params
        
        return int(total_params)
    
    def validate_model_config_compatibility(self, checkpoint_path: str) -> bool:
        """
        Validate that model configuration is compatible with checkpoint.
        
        Args:
            checkpoint_path: Path to checkpoint file
            
        Returns:
            True if compatible, False otherwise
        """
        try:
            checkpoint_manager = CheckpointManager(checkpoint_path)
            checkpoint_info = checkpoint_manager.get_checkpoint_info()
            
            # Basic compatibility checks
            if "model" not in checkpoint_info:
                logger.error("Checkpoint does not contain model state")
                return False
            
            # Additional checks could be added here
            logger.info("Model configuration appears compatible with checkpoint")
            return True
            
        except Exception as e:
            logger.error(f"Failed to validate compatibility: {e}")
            return False 