#!/usr/bin/env python3
"""
Working example of how to use the ModernBERT inference system.

This script demonstrates how to use the ModernBERT inference system for:
1. Masked Language Modeling (MLM) - predicting masked tokens
2. Embedding generation - creating sentence embeddings

Usage:
    python inference/working_example.py
    python inference/working_example.py --config_path path/to/config.yaml
    python inference/working_example.py --config_path path/to/config.yaml --checkpoint_path path/to/checkpoint.pt
    python inference/working_example.py --help
"""

import sys
import argparse
from pathlib import Path

# Add project root to path (use this in Jupyter notebook)
sys.path.insert(0, str(Path.cwd()))

# Import directly from the module (bypasses problematic package imports)
from inference import ModernBERTInference

def parse_arguments():
    """Parse command-line arguments."""
    parser = argparse.ArgumentParser(
        description="ModernBERT Inference Working Example",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python inference/working_example.py
  python inference/working_example.py --config_path path/to/config.yaml
  python inference/working_example.py --config_path path/to/config.yaml --checkpoint_path path/to/checkpoint.pt
        """
    )

    # Default paths
    default_config = "yamls/main/flex-bert-modernbert-base-edu-fw-320B-paper-custom-tokenizer.yaml"
    default_checkpoint = "/s2_nfs/ckpt/flex-bert-modernbert-base-edu-fw-320B-article-custom-tokenizer/ep1-ba114000-rank0.pt"

    parser.add_argument(
        "--config_path",
        type=str,
        default=default_config,
        help=f"Path to the model configuration YAML file (default: {default_config})"
    )

    parser.add_argument(
        "--checkpoint_path",
        type=str,
        default=default_checkpoint,
        help=f"Path to the model checkpoint file (default: {default_checkpoint})"
    )

    parser.add_argument(
        "--device",
        type=str,
        default="auto",
        choices=["auto", "cpu", "cuda", "mps"],
        help="Device to use for inference (default: auto)"
    )

    parser.add_argument(
        "--precision",
        type=str,
        default="fp32",
        choices=["fp32", "fp16", "bf16"],
        help="Precision to use for inference (default: fp32)"
    )

    return parser.parse_args()


def main():
    """Demo of the inference system."""

    # Parse command-line arguments
    args = parse_arguments()

    config_path = args.config_path
    checkpoint_path = args.checkpoint_path
    device = args.device
    precision = args.precision

    print(f"📁 Config path: {config_path}")
    print(f"📁 Checkpoint path: {checkpoint_path}")
    print(f"🖥️  Device: {device}")
    print(f"🔢 Precision: {precision}")
    print()
    
    
    print("🚀 ModernBERT Inference Example")
    print("=" * 50)
    
    try:
        # Initialize inference system
        print("1. Initializing inference system...")
        with ModernBERTInference(
            config_path=config_path,
            checkpoint_path=checkpoint_path,
            device=device,
            precision=precision
        ) as inference:
            
            # Get model info
            print("2. Getting model information...")
            model_info = inference.get_model_info()
            print(f"   ✓ Model loaded: {model_info.get('parameter_count', 'Unknown')} parameters")
            print(f"   ✓ Device: {model_info.get('device', 'Unknown')}")
            print(f"   ✓ Padding: {'unpadded' if model_info.get('is_unpadded', False) else 'padded'}")
            
            # MLM inference example
            print("\n3. Testing MLM inference...")
            text_with_mask = "The capital of France is <mask>."
            print(f"   Input: {text_with_mask}")
            
            predictions = inference.predict_masked_tokens(text_with_mask, top_k=3)
            print("   Predictions:")
            
            if isinstance(predictions, dict) and "mask_predictions" in predictions:
                for mask_pred in predictions["mask_predictions"]:
                    for i, pred in enumerate(mask_pred["predictions"][:3]):
                        print(f"     {i+1}. {pred['token']} (prob: {pred['probability']:.3f})")
            
            # Embedding generation example
            print("\n4. Testing embedding generation...")
            sentences = [
                "This is a sentence about machine learning.",
                "The apple is red."
            ]
            
            embeddings = inference.encode_texts(sentences, normalize=True)
            print(f"   Generated embeddings shape: {embeddings.shape}")
            print(f"   Embedding dimension: {inference.get_embedding_dimension()}")
            
            # Similarity computation
            similarity = inference.compute_similarity(sentences[0], sentences[1])
            print(f"   Similarity between sentences: {similarity:.3f}")
            
            print("\n✅ All tests completed successfully!")
            print("🎉 Your inference system is working correctly!")

    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

        print("\n🔧 Troubleshooting tips:")
        print("1. Make sure your checkpoint path is correct")
        print("2. Verify the config YAML file exists")
        print("3. Check that you have enough GPU memory")
        print("4. Try setting device='cpu' if GPU issues persist")

    # Print usage examples
    print_usage_examples(config_path, checkpoint_path, device, precision)


def print_usage_examples(config_path, checkpoint_path, device, precision):
    """Print usage examples for command line and Jupyter notebook."""
    print("\n" + "=" * 60)
    print("📝 USAGE EXAMPLES:")
    print("=" * 60)
    print("""
Command Line Usage:
  # Use default paths
  python inference/working_example.py

  # Specify custom config
  python inference/working_example.py --config_path path/to/your/config.yaml

  # Specify both config and checkpoint
  python inference/working_example.py \\
    --config_path path/to/your/config.yaml \\
    --checkpoint_path path/to/your/checkpoint.pt

  # Use different device/precision
  python inference/working_example.py --device cuda --precision fp16
  python inference/working_example.py --device mps --precision fp16
  python inference/working_example.py --device cpu --precision fp32

Jupyter Notebook Usage:
  # Copy this code into your Jupyter notebook:

  import sys
  from pathlib import Path

  # Add project root to path
  sys.path.insert(0, str(Path.cwd()))

  # Import the inference class
  from inference.inference import ModernBERTInference

  # Use your actual paths (update these to your paths)""")

    print(f'  config_path = "{config_path}"')
    print(f'  checkpoint_path = "{checkpoint_path}"')
    print()
    print(f'  # Initialize and use')
    print(f'  with ModernBERTInference(config_path, checkpoint_path, device="{device}", precision="{precision}") as inference:')
    print("""      # MLM inference
      predictions = inference.predict_masked_tokens("The capital of France is <mask>.")
      print("Predictions:", predictions)

      # Embedding generation
      embeddings = inference.encode_texts(["This is a sentence.", "This is another sentence."])
      print("Embeddings shape:", embeddings.shape)
""")


if __name__ == "__main__":
    main()