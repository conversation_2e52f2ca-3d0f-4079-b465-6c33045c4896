#!/usr/bin/env python3
# Copyright 2024 onwards Answer.AI, LightOn, and contributors
# License: Apache-2.0

"""
Test script for enhanced device handling in ModernBERT inference.

Tests device detection, selection, and fallback mechanisms across
different compute platforms (CUDA, MPS, CPU).
"""

import sys
import os
from pathlib import Path
import torch
import logging

# Add project root and inference directory to path
project_root = Path(__file__).parent.parent
inference_dir = Path(__file__).parent
sys.path.insert(0, str(project_root))
sys.path.insert(0, str(inference_dir))

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def test_device_detection():
    """Test device detection capabilities."""
    print("\n" + "="*60)
    print("TESTING DEVICE DETECTION")
    print("="*60)
    
    try:
        from utils.device_utils import device_manager
        
        # Detect all available devices
        devices = device_manager.detect_available_devices()
        
        print(f"Found {len(devices)} compute devices:")
        for i, device_info in enumerate(devices):
            print(f"\n{i+1}. {device_info.device_name}")
            print(f"   Type: {device_info.device_type}")
            print(f"   Available: {device_info.is_available}")
            print(f"   Flash Attention: {device_info.supports_flash_attention}")
            print(f"   Mixed Precision: {device_info.supports_mixed_precision}")
            
            if device_info.memory_total:
                memory_gb = device_info.memory_total / (1024**3)
                print(f"   Memory: {memory_gb:.1f} GB")
            
            if device_info.compute_capability:
                print(f"   Compute Capability: {device_info.compute_capability}")
        
        return True
        
    except Exception as e:
        print(f"❌ Device detection failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_device_selection():
    """Test device selection with different preferences."""
    print("\n" + "="*60)
    print("TESTING DEVICE SELECTION")
    print("="*60)
    
    try:
        from utils.device_utils import device_manager
        
        test_preferences = ["auto", "cpu"]
        
        # Add CUDA if available
        if torch.cuda.is_available():
            test_preferences.extend(["cuda", "cuda:0"])
        
        # Add MPS if available
        if hasattr(torch.backends, 'mps') and torch.backends.mps.is_available():
            test_preferences.append("mps")
        
        for preference in test_preferences:
            print(f"\nTesting device preference: '{preference}'")
            try:
                device, device_info = device_manager.select_best_device(preference)
                print(f"✓ Selected: {device} ({device_info.device_name})")
                
                # Test device compatibility
                is_compatible, warnings = device_manager.validate_device_compatibility(
                    device_info, requires_flash_attention=True
                )
                
                if warnings:
                    for warning in warnings:
                        print(f"  ⚠ {warning}")
                
            except Exception as e:
                print(f"❌ Failed to select device '{preference}': {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ Device selection test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_inference_config():
    """Test inference configuration with device handling."""
    print("\n" + "="*60)
    print("TESTING INFERENCE CONFIGURATION")
    print("="*60)
    
    try:
        from config.inference_config import InferenceConfig
        
        test_configs = [
            {"device": "auto", "precision": "fp32"},
            {"device": "cpu", "precision": "fp32"},
        ]
        
        # Add CUDA tests if available
        if torch.cuda.is_available():
            test_configs.extend([
                {"device": "cuda", "precision": "fp16"},
                {"device": "cuda", "precision": "bf16"},
            ])
        
        # Add MPS tests if available
        if hasattr(torch.backends, 'mps') and torch.backends.mps.is_available():
            test_configs.extend([
                {"device": "mps", "precision": "fp16"},
                {"device": "mps", "precision": "bf16"},  # Should fallback to fp16
            ])
        
        for config_dict in test_configs:
            print(f"\nTesting config: {config_dict}")
            try:
                config = InferenceConfig(**config_dict)
                
                print(f"✓ Device: {config.device} -> {config.selected_device}")
                print(f"✓ Precision: {config.precision} ({config.torch_dtype})")
                
                if config.device_info:
                    print(f"✓ Device Info: {config.device_info.device_name}")
                    print(f"  Flash Attention: {config.supports_flash_attention}")
                
            except Exception as e:
                print(f"❌ Config failed: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ Inference config test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_flash_attention():
    """Test Flash Attention compatibility detection."""
    print("\n" + "="*60)
    print("TESTING FLASH ATTENTION COMPATIBILITY")
    print("="*60)
    
    try:
        from utils.flash_attention_utils import flash_attention_manager
        
        # Test global Flash Attention detection
        flash_info = flash_attention_manager.detect_flash_attention()
        print(f"Flash Attention globally available: {flash_info.is_available}")
        if flash_info.version:
            print(f"Version: {flash_info.version}")
        if flash_info.fallback_reason:
            print(f"Fallback reason: {flash_info.fallback_reason}")
        
        # Test device-specific compatibility
        test_devices = [torch.device("cpu")]
        
        if torch.cuda.is_available():
            test_devices.append(torch.device("cuda"))
        
        if hasattr(torch.backends, 'mps') and torch.backends.mps.is_available():
            test_devices.append(torch.device("mps"))
        
        for device in test_devices:
            print(f"\nTesting Flash Attention on {device}:")
            device_flash_info = flash_attention_manager.check_device_compatibility(device)
            print(f"  Supported: {device_flash_info.supports_device}")
            print(f"  Rotary embeddings: {device_flash_info.supports_rotary}")
            if device_flash_info.fallback_reason:
                print(f"  Fallback reason: {device_flash_info.fallback_reason}")
            
            # Get attention config
            attention_config = flash_attention_manager.get_attention_config(device)
            print(f"  Config: {attention_config}")
        
        return True
        
    except Exception as e:
        print(f"❌ Flash Attention test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_error_handling():
    """Test error handling capabilities."""
    print("\n" + "="*60)
    print("TESTING ERROR HANDLING")
    print("="*60)
    
    try:
        from utils.error_handling import device_error_handler
        
        # Test with a fake CUDA error
        fake_device = torch.device("cuda:99")  # Non-existent device
        
        try:
            # This should fail
            test_tensor = torch.tensor([1.0], device=fake_device)
        except Exception as e:
            print("Testing error handling with fake CUDA error:")
            error_info = device_error_handler.handle_device_error(e, fake_device, "test")
            device_error_handler.log_error_info(error_info)
            
            # Test fallback suggestions
            suggestions = device_error_handler.suggest_device_fallback(fake_device)
            print(f"Fallback suggestions: {suggestions}")
        
        # Test memory info
        if torch.cuda.is_available():
            memory_info = device_error_handler.get_memory_usage_info(torch.device("cuda"))
            print(f"\nCUDA memory info: {memory_info}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error handling test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """Run all device handling tests."""
    print("🚀 ModernBERT Device Handling Test Suite")
    print("=" * 60)
    
    # System info
    print(f"Python version: {sys.version}")
    print(f"PyTorch version: {torch.__version__}")
    print(f"CUDA available: {torch.cuda.is_available()}")
    if torch.cuda.is_available():
        print(f"CUDA devices: {torch.cuda.device_count()}")
    print(f"MPS available: {hasattr(torch.backends, 'mps') and torch.backends.mps.is_available()}")
    
    # Run tests
    tests = [
        ("Device Detection", test_device_detection),
        ("Device Selection", test_device_selection),
        ("Inference Configuration", test_inference_config),
        ("Flash Attention", test_flash_attention),
        ("Error Handling", test_error_handling),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            if test_func():
                print(f"✅ {test_name} PASSED")
                passed += 1
            else:
                print(f"❌ {test_name} FAILED")
        except Exception as e:
            print(f"💥 {test_name} CRASHED: {e}")
            import traceback
            traceback.print_exc()
    
    # Summary
    print("\n" + "="*60)
    print(f"TEST RESULTS: {passed}/{total} tests passed")
    print("="*60)
    
    if passed == total:
        print("🎉 All device handling tests passed!")
        print("\nThe enhanced device handling is working correctly.")
    else:
        print("⚠ Some tests failed. Check the output above for details.")
    
    return passed == total


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
