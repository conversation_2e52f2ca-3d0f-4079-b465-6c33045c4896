#!/usr/bin/env python3
# Copyright 2024 onwards Answer.AI, LightOn, and contributors
# License: Apache-2.0

"""
Example script demonstrating efficient batch inference with ModernBERT.

This script shows how to:
1. Process large datasets efficiently using batching
2. Optimize memory usage with the bert24 conda environment 
3. Handle both MLM and embedding tasks at scale
4. Monitor performance and memory usage
"""

import sys
import time
import os
from pathlib import Path
import numpy as np

# Add inference directory to path
sys.path.append(str(Path(__file__).parent.parent))

from inference import ModernBERTInference


def generate_sample_data(num_samples: int = 100):
    """Generate sample data for testing batch inference."""
    
    # Sample texts for MLM (with masks)
    mlm_templates = [
        "The [MASK] of artificial intelligence is rapidly advancing.",
        "Machine learning [MASK] are becoming more sophisticated.",
        "Natural language processing helps [MASK] understand text.",
        "Deep learning [MASK] require significant computational resources.",
        "The future of [MASK] looks very promising.",
        "Researchers are developing new [MASK] for better performance.",
        "Data science involves [MASK] large amounts of information.",
        "Neural networks can [MASK] complex patterns in data.",
        "Computer vision [MASK] are used in many applications.",
        "Transformers have [MASK] the field of NLP."
    ]
    
    # Sample texts for embeddings (no masks)
    embedding_texts = [
        "Artificial intelligence is transforming industries worldwide.",
        "Machine learning algorithms can process vast amounts of data.",
        "Natural language processing enables computers to understand human language.",
        "Deep learning models require extensive training on large datasets.",
        "Computer vision technology recognizes patterns in images and videos.",
        "Data science combines statistics, programming, and domain expertise.",
        "Neural networks are inspired by the structure of the human brain.",
        "Big data analytics helps organizations make informed decisions.",
        "Cloud computing provides scalable infrastructure for AI applications.",
        "Robotics integrates AI to create intelligent autonomous systems."
    ]
    
    # Create larger datasets by cycling through templates
    mlm_data = [mlm_templates[i % len(mlm_templates)] for i in range(num_samples)]
    embedding_data = [embedding_texts[i % len(embedding_texts)] for i in range(num_samples)]
    
    return mlm_data, embedding_data


def time_function(func, *args, **kwargs):
    """Time a function execution."""
    start_time = time.time()
    result = func(*args, **kwargs)
    end_time = time.time()
    return result, end_time - start_time


def main():
    # Update these paths to your actual checkpoint
    config_path = "yamls/main/flex-bert-modernbert-base-edu-fw-320B-paper-custom-tokenizer.yaml"
    checkpoint_path = "ckpt/your-checkpoint/latest-rank0.pt"
    
    print("=" * 60)
    print("ModernBERT Batch Inference Example")
    print("=" * 60)
    
    # Check if running in bert24 environment
    conda_env = os.environ.get('CONDA_DEFAULT_ENV', 'unknown')
    print(f"Conda environment: {conda_env}")
    if conda_env == 'bert24':
        print("✓ Running in recommended bert24 environment")
    else:
        print("⚠ Consider activating the bert24 conda environment for optimal performance")
    
    # Initialize inference
    try:
        print("\nInitializing ModernBERT Inference...")
        
        with ModernBERTInference(
            config_path=config_path,
            checkpoint_path=checkpoint_path,
            device="auto",
            precision="fp32",
            max_batch_size=16  # Adjust based on your GPU memory
        ) as inference:
            
            # Get model info
            model_info = inference.get_model_info()
            print(f"Model loaded: {model_info['parameter_count']:,} parameters")
            print(f"Device: {model_info['device']}")
            print(f"Padding configuration: {model_info['is_unpadded']}")
            
            # Test different batch sizes
            batch_sizes = [1, 4, 8, 16]
            sample_sizes = [10, 50, 100]
            
            for sample_size in sample_sizes:
                print(f"\n" + "=" * 40)
                print(f"Testing with {sample_size} samples")
                print("=" * 40)
                
                # Generate sample data
                mlm_data, embedding_data = generate_sample_data(sample_size)
                
                # Test MLM batch inference
                print(f"\n--- MLM Batch Inference ---")
                
                for batch_size in batch_sizes:
                    if batch_size > sample_size:
                        continue
                    
                    try:
                        predictions, elapsed_time = time_function(
                            inference.batch_predict_masked_tokens,
                            mlm_data,
                            batch_size=batch_size,
                            top_k=3
                        )
                        
                        samples_per_second = sample_size / elapsed_time
                        
                        print(f"  Batch size {batch_size:2d}: "
                              f"{elapsed_time:.2f}s ({samples_per_second:.1f} samples/s)")
                        
                        # Show sample predictions for first batch
                        if batch_size == batch_sizes[0]:
                            print(f"    Sample prediction: {mlm_data[0]}")
                            if predictions and "mask_predictions" in predictions[0]:
                                for pred in predictions[0]["mask_predictions"][:1]:
                                    top_pred = pred["predictions"][0] if pred["predictions"] else None
                                    if top_pred:
                                        print(f"    -> {top_pred['token']} (prob: {top_pred['probability']:.3f})")
                    
                    except Exception as e:
                        print(f"  Batch size {batch_size:2d}: Error - {e}")
                
                # Test Embedding batch inference
                print(f"\n--- Embedding Batch Inference ---")
                
                for batch_size in batch_sizes:
                    if batch_size > sample_size:
                        continue
                    
                    try:
                        embeddings, elapsed_time = time_function(
                            inference.batch_encode_texts,
                            embedding_data,
                            batch_size=batch_size,
                            normalize=True
                        )
                        
                        samples_per_second = sample_size / elapsed_time
                        
                        print(f"  Batch size {batch_size:2d}: "
                              f"{elapsed_time:.2f}s ({samples_per_second:.1f} samples/s)")
                        
                        # Show embedding stats for first batch
                        if batch_size == batch_sizes[0]:
                            print(f"    Embeddings shape: {embeddings.shape}")
                            print(f"    Average norm: {np.mean(np.linalg.norm(embeddings, axis=1)):.3f}")
                    
                    except Exception as e:
                        print(f"  Batch size {batch_size:2d}: Error - {e}")
            
            # Memory efficiency demonstration
            print(f"\n" + "=" * 40)
            print("Memory Efficiency Test")
            print("=" * 40)
            
            # Test with larger dataset to show memory management
            large_sample_size = 200
            large_mlm_data, large_embedding_data = generate_sample_data(large_sample_size)
            
            print(f"\nProcessing {large_sample_size} samples with memory-efficient batching...")
            
            # MLM with automatic batching
            start_time = time.time()
            large_predictions = inference.batch_predict_masked_tokens(
                large_mlm_data,
                batch_size=8,  # Smaller batch size for memory efficiency
                top_k=1
            )
            mlm_time = time.time() - start_time
            
            print(f"MLM processing: {mlm_time:.2f}s ({large_sample_size/mlm_time:.1f} samples/s)")
            print(f"Successfully processed {len(large_predictions)} predictions")
            
            # Embedding with automatic batching
            start_time = time.time()
            large_embeddings = inference.batch_encode_texts(
                large_embedding_data,
                batch_size=8,
                normalize=True
            )
            embedding_time = time.time() - start_time
            
            print(f"Embedding processing: {embedding_time:.2f}s ({large_sample_size/embedding_time:.1f} samples/s)")
            print(f"Generated embeddings: {large_embeddings.shape}")
            
            # Similarity computation example
            print(f"\n--- Efficient Similarity Computation ---")
            
            query_texts = [
                "What is machine learning?",
                "How does artificial intelligence work?",
                "Explain deep learning concepts."
            ]
            
            # Encode query texts
            query_embeddings = inference.encode_texts(query_texts, normalize=True)
            
            # Compute similarities with the large dataset
            start_time = time.time()
            similarities = np.dot(query_embeddings, large_embeddings.T)
            similarity_time = time.time() - start_time
            
            print(f"Computed {similarities.shape[0] * similarities.shape[1]:,} similarities in {similarity_time:.3f}s")
            
            # Find most similar documents for each query
            for i, query in enumerate(query_texts):
                top_indices = np.argsort(similarities[i])[::-1][:3]
                print(f"\nQuery: {query}")
                print("Top 3 most similar documents:")
                for j, idx in enumerate(top_indices):
                    print(f"  {j+1}. {similarities[i,idx]:.3f} - {large_embedding_data[idx][:60]}...")
            
            print(f"\n" + "=" * 60)
            print("Batch inference examples completed successfully!")
            print("=" * 60)
            
    except FileNotFoundError as e:
        print(f"Error: {e}")
        print("\nPlease update the config_path and checkpoint_path variables with your actual file paths.")
        print("Make sure you have:")
        print("1. A trained ModernBERT checkpoint (.pt file)")
        print("2. The corresponding training configuration YAML file")
    except Exception as e:
        print(f"An error occurred: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main() 