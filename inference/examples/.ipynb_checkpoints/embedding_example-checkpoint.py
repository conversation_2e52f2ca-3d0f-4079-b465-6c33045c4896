#!/usr/bin/env python3
# Copyright 2024 onwards Answer.AI, LightOn, and contributors
# License: Apache-2.0

"""
Example script demonstrating embedding generation with ModernBERT.

This script shows how to:
1. Generate sentence embeddings from text
2. Compute text similarities
3. Use different pooling strategies
4. Handle both padded and unpadded configurations automatically
"""

import sys
from pathlib import Path
import numpy as np

# Add inference directory to path
sys.path.append(str(Path(__file__).parent.parent))

from inference import ModernBERTInference


def main():
    # Example paths - update these to your actual paths
    config_path = "yamls/main/flex-bert-modernbert-base-edu-fw-320B-paper-custom-tokenizer.yaml"
    checkpoint_path = "ckpt/your-checkpoint/latest-rank0.pt"
    
    print("Initializing ModernBERT Inference for Embeddings...")
    
    # Initialize inference with automatic padding/unpadding detection
    try:
        with ModernBERTInference(
            config_path=config_path,
            checkpoint_path=checkpoint_path,
            device="auto",
            precision="fp32"
        ) as inference:
            
            # Get model info
            model_info = inference.get_model_info()
            print(f"Model loaded: {model_info['parameter_count']:,} parameters")
            print(f"Embedding dimension: {inference.get_embedding_dimension()}")
            print(f"Padding configuration: {model_info['is_unpadded']}")
            
            # Example 1: Single sentence embedding
            print("\n=== Example 1: Single Sentence Embedding ===")
            text = "Natural language processing is a fascinating field."
            embedding = inference.encode_texts(text)
            
            print(f"Input: {text}")
            print(f"Embedding shape: {embedding.shape}")
            print(f"Embedding norm: {np.linalg.norm(embedding):.4f}")
            print(f"First 10 values: {embedding[:10]}")
            
            # Example 2: Batch embedding generation
            print("\n=== Example 2: Batch Embedding Generation ===")
            sentences = [
                "I love machine learning.",
                "Deep learning models are powerful.",
                "Natural language processing is exciting.",
                "Transformers revolutionized NLP.",
                "BERT is a bidirectional encoder."
            ]
            
            embeddings = inference.encode_texts(sentences, normalize=True)
            
            print(f"Input sentences: {len(sentences)}")
            print(f"Embeddings shape: {embeddings.shape}")
            print("Embedding norms (should be ~1.0 since normalized):")
            for i, sentence in enumerate(sentences):
                norm = np.linalg.norm(embeddings[i])
                print(f"  {i+1}. {norm:.4f} - {sentence}")
            
            # Example 3: Similarity computation
            print("\n=== Example 3: Similarity Computation ===")
            sentence1 = "I enjoy studying artificial intelligence."
            sentence2 = "Machine learning is my favorite subject."
            sentence3 = "The weather is nice today."
            
            similarity_12 = inference.compute_similarity(sentence1, sentence2)
            similarity_13 = inference.compute_similarity(sentence1, sentence3)
            similarity_23 = inference.compute_similarity(sentence2, sentence3)
            
            print(f"Sentence 1: {sentence1}")
            print(f"Sentence 2: {sentence2}")
            print(f"Sentence 3: {sentence3}")
            print(f"\nSimilarities:")
            print(f"  Sentence 1 ↔ Sentence 2: {similarity_12:.4f}")
            print(f"  Sentence 1 ↔ Sentence 3: {similarity_13:.4f}")
            print(f"  Sentence 2 ↔ Sentence 3: {similarity_23:.4f}")
            
            # Example 4: Different pooling strategies
            print("\n=== Example 4: Different Pooling Strategies ===")
            text = "Pooling strategies affect the final embedding."
            
            pooling_strategies = ["mean", "cls", "max", "mean_sqrt_len"]
            
            print(f"Input: {text}")
            print("Embeddings with different pooling strategies:")
            
            for strategy in pooling_strategies:
                try:
                    embedding = inference.encode_texts(text, pooling_strategy=strategy)
                    norm = np.linalg.norm(embedding)
                    print(f"  {strategy:12} - norm: {norm:.4f}, first 5: {embedding[:5]}")
                except Exception as e:
                    print(f"  {strategy:12} - Error: {e}")
            
            # Example 5: Similarity matrix for multiple sentences
            print("\n=== Example 5: Similarity Matrix ===")
            topics = [
                "Artificial intelligence and machine learning",
                "Deep neural networks and transformers", 
                "Computer vision and image recognition",
                "Natural language processing and BERT",
                "Climate change and global warming"
            ]
            
            topic_embeddings = inference.encode_texts(topics, normalize=True)
            
            # Compute pairwise similarities
            similarity_matrix = np.dot(topic_embeddings, topic_embeddings.T)
            
            print("Topics:")
            for i, topic in enumerate(topics):
                print(f"  {i+1}. {topic}")
            
            print("\nSimilarity Matrix:")
            print("     ", end="")
            for j in range(len(topics)):
                print(f"{j+1:6}", end="")
            print()
            
            for i in range(len(topics)):
                print(f"{i+1:2}. ", end="")
                for j in range(len(topics)):
                    print(f"{similarity_matrix[i,j]:6.3f}", end="")
                print()
            
            # Example 6: Finding most similar sentences
            print("\n=== Example 6: Finding Most Similar Sentences ===")
            query = "What is the best deep learning framework?"
            
            candidates = [
                "PyTorch is a popular deep learning library.",
                "TensorFlow provides comprehensive ML tools.",
                "The weather forecast shows rain tomorrow.",
                "Keras makes neural networks easy to build.",
                "I prefer coffee over tea in the morning.",
                "Transformers are the state-of-the-art in NLP.",
                "My favorite programming language is Python."
            ]
            
            # Compute similarities
            similarities = []
            for candidate in candidates:
                sim = inference.compute_similarity(query, candidate)
                similarities.append(sim)
            
            # Sort by similarity
            sorted_indices = np.argsort(similarities)[::-1]
            
            print(f"Query: {query}")
            print("\nMost similar candidates:")
            for i, idx in enumerate(sorted_indices[:5]):
                print(f"  {i+1}. {similarities[idx]:.4f} - {candidates[idx]}")
            
            # Example 7: Embedding for clustering/retrieval
            print("\n=== Example 7: Embeddings for Different Tasks ===")
            documents = [
                "Introduction to machine learning algorithms.",
                "Overview of deep neural network architectures.",
                "Guide to natural language processing techniques."
            ]
            
            # Embeddings optimized for clustering
            clustering_embeddings = inference.encode_texts(
                documents, 
                pooling_strategy="mean", 
                normalize=True
            )
            
            print("Document embeddings for clustering/retrieval:")
            for i, doc in enumerate(documents):
                print(f"  Doc {i+1}: {doc}")
                print(f"          Embedding norm: {np.linalg.norm(clustering_embeddings[i]):.4f}")
            
    except FileNotFoundError as e:
        print(f"Error: {e}")
        print("Please update the config_path and checkpoint_path variables with your actual file paths.")
    except Exception as e:
        print(f"An error occurred: {e}")
        raise


if __name__ == "__main__":
    main() 