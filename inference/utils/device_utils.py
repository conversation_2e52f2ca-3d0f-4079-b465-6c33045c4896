# Copyright 2024 onwards Answer.AI, LightOn, and contributors
# License: Apache-2.0

"""
Device detection and management utilities for ModernBERT inference.

Provides robust device detection with automatic fallback support for:
- CUDA GPUs (Linux/Windows)
- MPS (Apple Silicon Mac)
- CPU (fallback)
"""

import torch
import logging
import platform
import subprocess
from typing import Dict, Any, Optional, Tuple, List
from dataclasses import dataclass

logger = logging.getLogger(__name__)


@dataclass
class DeviceInfo:
    """Information about a compute device."""
    device_type: str  # "cuda", "mps", "cpu"
    device_name: str  # Human-readable device name
    device_id: Optional[int] = None  # Device ID for multi-GPU systems
    memory_total: Optional[int] = None  # Total memory in bytes
    memory_available: Optional[int] = None  # Available memory in bytes
    compute_capability: Optional[Tuple[int, int]] = None  # CUDA compute capability
    is_available: bool = True
    supports_flash_attention: bool = False
    supports_mixed_precision: bool = False


class DeviceManager:
    """
    Comprehensive device detection and management for ModernBERT inference.
    
    Handles automatic device selection with fallback support and provides
    detailed device information for optimization decisions.
    """
    
    def __init__(self):
        """Initialize device manager."""
        self._device_cache = {}
        self._detected_devices = None
    
    def detect_available_devices(self) -> List[DeviceInfo]:
        """
        Detect all available compute devices.
        
        Returns:
            List of DeviceInfo objects for available devices
        """
        if self._detected_devices is not None:
            return self._detected_devices
        
        devices = []
        
        # Detect CUDA devices
        cuda_devices = self._detect_cuda_devices()
        devices.extend(cuda_devices)
        
        # Detect MPS device (Apple Silicon)
        mps_device = self._detect_mps_device()
        if mps_device:
            devices.append(mps_device)
        
        # CPU is always available
        cpu_device = self._detect_cpu_device()
        devices.append(cpu_device)
        
        self._detected_devices = devices
        logger.info(f"Detected {len(devices)} compute devices")
        
        return devices
    
    def _detect_cuda_devices(self) -> List[DeviceInfo]:
        """Detect CUDA GPU devices."""
        devices = []
        
        if not torch.cuda.is_available():
            logger.debug("CUDA not available")
            return devices
        
        try:
            device_count = torch.cuda.device_count()
            logger.info(f"Found {device_count} CUDA device(s)")
            
            for i in range(device_count):
                device_props = torch.cuda.get_device_properties(i)
                device_name = torch.cuda.get_device_name(i)
                
                # Get memory information
                memory_total = device_props.total_memory
                torch.cuda.set_device(i)
                memory_available = memory_total - torch.cuda.memory_allocated(i)
                
                # Check compute capability
                compute_capability = (device_props.major, device_props.minor)
                
                # Flash Attention support (requires compute capability >= 7.5)
                supports_flash_attention = (
                    compute_capability[0] > 7 or 
                    (compute_capability[0] == 7 and compute_capability[1] >= 5)
                )
                
                device_info = DeviceInfo(
                    device_type="cuda",
                    device_name=device_name,
                    device_id=i,
                    memory_total=memory_total,
                    memory_available=memory_available,
                    compute_capability=compute_capability,
                    is_available=True,
                    supports_flash_attention=supports_flash_attention,
                    supports_mixed_precision=True  # CUDA generally supports mixed precision
                )
                
                devices.append(device_info)
                logger.debug(f"CUDA device {i}: {device_name} "
                           f"({memory_total // 1024**3}GB, CC {compute_capability})")
        
        except Exception as e:
            logger.warning(f"Error detecting CUDA devices: {e}")
        
        return devices
    
    def _detect_mps_device(self) -> Optional[DeviceInfo]:
        """Detect MPS device (Apple Silicon)."""
        if not hasattr(torch.backends, 'mps') or not torch.backends.mps.is_available():
            logger.debug("MPS not available")
            return None
        
        try:
            # Get system information for Apple Silicon
            system_info = self._get_apple_silicon_info()
            
            device_info = DeviceInfo(
                device_type="mps",
                device_name=system_info.get("chip_name", "Apple Silicon GPU"),
                device_id=None,
                memory_total=system_info.get("memory_total"),
                memory_available=system_info.get("memory_available"),
                compute_capability=None,
                is_available=True,
                supports_flash_attention=False,  # Flash Attention not supported on MPS
                supports_mixed_precision=True   # MPS supports mixed precision
            )
            
            logger.info(f"Found MPS device: {device_info.device_name}")
            return device_info
        
        except Exception as e:
            logger.warning(f"Error detecting MPS device: {e}")
            return None
    
    def _detect_cpu_device(self) -> DeviceInfo:
        """Detect CPU device (always available)."""
        try:
            # Get CPU information
            cpu_info = self._get_cpu_info()
            
            device_info = DeviceInfo(
                device_type="cpu",
                device_name=cpu_info.get("name", "CPU"),
                device_id=None,
                memory_total=cpu_info.get("memory_total"),
                memory_available=cpu_info.get("memory_available"),
                compute_capability=None,
                is_available=True,
                supports_flash_attention=False,  # Flash Attention requires GPU
                supports_mixed_precision=False  # Mixed precision not recommended on CPU
            )
            
            logger.debug(f"CPU device: {device_info.device_name}")
            return device_info
        
        except Exception as e:
            logger.warning(f"Error getting CPU info: {e}")
            # Return basic CPU info as fallback
            return DeviceInfo(
                device_type="cpu",
                device_name="CPU",
                is_available=True,
                supports_flash_attention=False,
                supports_mixed_precision=False
            )
    
    def _get_apple_silicon_info(self) -> Dict[str, Any]:
        """Get Apple Silicon system information."""
        info = {}
        
        try:
            if platform.system() == "Darwin":
                # Get chip information
                result = subprocess.run(
                    ["system_profiler", "SPHardwareDataType"],
                    capture_output=True, text=True, timeout=10
                )
                
                if result.returncode == 0:
                    output = result.stdout
                    for line in output.split('\n'):
                        if 'Chip:' in line:
                            info['chip_name'] = line.split('Chip:')[1].strip()
                        elif 'Memory:' in line:
                            memory_str = line.split('Memory:')[1].strip()
                            # Parse memory (e.g., "16 GB" -> bytes)
                            if 'GB' in memory_str:
                                gb = int(memory_str.split()[0])
                                info['memory_total'] = gb * 1024**3
        
        except Exception as e:
            logger.debug(f"Could not get Apple Silicon info: {e}")
        
        return info
    
    def _get_cpu_info(self) -> Dict[str, Any]:
        """Get CPU information."""
        info = {}
        
        try:
            import psutil
            
            # CPU name
            if platform.system() == "Darwin":
                result = subprocess.run(
                    ["sysctl", "-n", "machdep.cpu.brand_string"],
                    capture_output=True, text=True, timeout=5
                )
                if result.returncode == 0:
                    info['name'] = result.stdout.strip()
            elif platform.system() == "Linux":
                with open('/proc/cpuinfo', 'r') as f:
                    for line in f:
                        if line.startswith('model name'):
                            info['name'] = line.split(':')[1].strip()
                            break
            
            # Memory information
            memory = psutil.virtual_memory()
            info['memory_total'] = memory.total
            info['memory_available'] = memory.available
        
        except ImportError:
            logger.debug("psutil not available for detailed CPU info")
        except Exception as e:
            logger.debug(f"Could not get CPU info: {e}")
        
        return info
    
    def select_best_device(self, preference: str = "auto") -> Tuple[torch.device, DeviceInfo]:
        """
        Select the best available device based on preference.
        
        Args:
            preference: Device preference ("auto", "cuda", "mps", "cpu", or specific like "cuda:0")
            
        Returns:
            Tuple of (torch.device, DeviceInfo)
        """
        devices = self.detect_available_devices()
        
        if preference == "auto":
            # Automatic selection with priority: CUDA > MPS > CPU
            for device_info in devices:
                if device_info.device_type == "cuda" and device_info.is_available:
                    device_str = f"cuda:{device_info.device_id}" if device_info.device_id is not None else "cuda"
                    return torch.device(device_str), device_info
            
            for device_info in devices:
                if device_info.device_type == "mps" and device_info.is_available:
                    return torch.device("mps"), device_info
            
            # Fallback to CPU
            cpu_device = next(d for d in devices if d.device_type == "cpu")
            return torch.device("cpu"), cpu_device
        
        else:
            # Specific device requested
            return self._select_specific_device(preference, devices)
    
    def _select_specific_device(self, device_str: str, devices: List[DeviceInfo]) -> Tuple[torch.device, DeviceInfo]:
        """Select a specific device."""
        if device_str.startswith("cuda"):
            # Handle cuda:N format
            if ":" in device_str:
                device_id = int(device_str.split(":")[1])
                cuda_devices = [d for d in devices if d.device_type == "cuda" and d.device_id == device_id]
                if cuda_devices:
                    return torch.device(device_str), cuda_devices[0]
                else:
                    raise ValueError(f"CUDA device {device_id} not available")
            else:
                # Just "cuda" - use first available CUDA device
                cuda_devices = [d for d in devices if d.device_type == "cuda"]
                if cuda_devices:
                    device_id = cuda_devices[0].device_id
                    device_str = f"cuda:{device_id}" if device_id is not None else "cuda"
                    return torch.device(device_str), cuda_devices[0]
                else:
                    raise ValueError("No CUDA devices available")
        
        elif device_str == "mps":
            mps_devices = [d for d in devices if d.device_type == "mps"]
            if mps_devices:
                return torch.device("mps"), mps_devices[0]
            else:
                raise ValueError("MPS device not available")
        
        elif device_str == "cpu":
            cpu_device = next(d for d in devices if d.device_type == "cpu")
            return torch.device("cpu"), cpu_device
        
        else:
            raise ValueError(f"Unsupported device specification: {device_str}")
    
    def get_device_memory_info(self, device: torch.device) -> Dict[str, int]:
        """Get memory information for a device."""
        if device.type == "cuda":
            torch.cuda.set_device(device)
            return {
                "total": torch.cuda.get_device_properties(device).total_memory,
                "allocated": torch.cuda.memory_allocated(device),
                "cached": torch.cuda.memory_reserved(device),
                "available": torch.cuda.get_device_properties(device).total_memory - torch.cuda.memory_allocated(device)
            }
        else:
            # For non-CUDA devices, return basic info
            return {"total": 0, "allocated": 0, "cached": 0, "available": 0}
    
    def validate_device_compatibility(self, device_info: DeviceInfo, 
                                    requires_flash_attention: bool = False,
                                    requires_mixed_precision: bool = False) -> Tuple[bool, List[str]]:
        """
        Validate device compatibility with requirements.
        
        Args:
            device_info: Device information
            requires_flash_attention: Whether Flash Attention is required
            requires_mixed_precision: Whether mixed precision is required
            
        Returns:
            Tuple of (is_compatible, list_of_warnings)
        """
        warnings = []
        is_compatible = True
        
        if requires_flash_attention and not device_info.supports_flash_attention:
            warnings.append(f"Flash Attention not supported on {device_info.device_name}, will use fallback")
        
        if requires_mixed_precision and not device_info.supports_mixed_precision:
            warnings.append(f"Mixed precision not recommended on {device_info.device_name}")
        
        return is_compatible, warnings


# Global device manager instance
device_manager = DeviceManager()
