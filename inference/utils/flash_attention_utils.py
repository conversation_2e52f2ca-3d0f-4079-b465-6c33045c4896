# Copyright 2024 onwards Answer.AI, LightOn, and contributors
# License: Apache-2.0

"""
Flash Attention compatibility utilities for ModernBERT inference.

Handles device-specific Flash Attention availability and provides
graceful fallbacks for unsupported devices.
"""

import torch
import logging
import importlib.util
from typing import Dict, Any, Optional, Tuple
from dataclasses import dataclass

logger = logging.getLogger(__name__)


@dataclass
class FlashAttentionInfo:
    """Information about Flash Attention availability and capabilities."""
    is_available: bool = False
    version: Optional[str] = None
    supports_device: bool = False
    supports_rotary: bool = False
    fallback_reason: Optional[str] = None


class FlashAttentionManager:
    """
    Manages Flash Attention compatibility across different devices.
    
    Provides device-specific Flash Attention detection and graceful
    fallbacks to PyTorch SDPA when Flash Attention is not available.
    """
    
    def __init__(self):
        """Initialize Flash Attention manager."""
        self._flash_info_cache = {}
        self._global_flash_info = None
    
    def detect_flash_attention(self) -> FlashAttentionInfo:
        """
        Detect Flash Attention availability globally.
        
        Returns:
            FlashAttentionInfo with global availability status
        """
        if self._global_flash_info is not None:
            return self._global_flash_info
        
        info = FlashAttentionInfo()
        
        try:
            # Check if flash_attn package is installed
            flash_attn_spec = importlib.util.find_spec("flash_attn")
            if flash_attn_spec is None:
                info.fallback_reason = "flash_attn package not installed"
                logger.debug("Flash Attention not available: package not installed")
                self._global_flash_info = info
                return info
            
            # Try to import flash_attn
            import flash_attn
            info.is_available = True
            info.version = getattr(flash_attn, '__version__', 'unknown')
            
            # Check version compatibility
            if hasattr(flash_attn, '__version__'):
                version = flash_attn.__version__
                if self._is_version_compatible(version):
                    logger.info(f"Flash Attention {version} detected")
                else:
                    info.is_available = False
                    info.fallback_reason = f"Incompatible version {version} (requires >= 2.5.7)"
                    logger.warning(f"Flash Attention version {version} is incompatible")
            
            # Test basic functionality
            if info.is_available:
                try:
                    from flash_attn import flash_attn_varlen_qkvpacked_func
                    from flash_attn.layers.rotary import RotaryEmbedding
                    info.supports_rotary = True
                    logger.debug("Flash Attention rotary embeddings available")
                except ImportError as e:
                    info.supports_rotary = False
                    logger.debug(f"Flash Attention rotary embeddings not available: {e}")
        
        except ImportError as e:
            info.fallback_reason = f"Import error: {e}"
            logger.debug(f"Flash Attention import failed: {e}")
        except Exception as e:
            info.fallback_reason = f"Unexpected error: {e}"
            logger.warning(f"Unexpected error detecting Flash Attention: {e}")
        
        self._global_flash_info = info
        return info
    
    def check_device_compatibility(self, device: torch.device) -> FlashAttentionInfo:
        """
        Check Flash Attention compatibility for a specific device.
        
        Args:
            device: Target device
            
        Returns:
            FlashAttentionInfo with device-specific compatibility
        """
        device_key = str(device)
        if device_key in self._flash_info_cache:
            return self._flash_info_cache[device_key]
        
        # Start with global Flash Attention info
        global_info = self.detect_flash_attention()
        device_info = FlashAttentionInfo(
            is_available=global_info.is_available,
            version=global_info.version,
            supports_rotary=global_info.supports_rotary,
            fallback_reason=global_info.fallback_reason
        )
        
        if not global_info.is_available:
            device_info.supports_device = False
            self._flash_info_cache[device_key] = device_info
            return device_info
        
        # Device-specific compatibility checks
        if device.type == "cuda":
            device_info.supports_device = self._check_cuda_compatibility(device)
            if not device_info.supports_device:
                device_info.fallback_reason = "CUDA device not compatible with Flash Attention"
        
        elif device.type == "mps":
            # Flash Attention is not supported on MPS
            device_info.supports_device = False
            device_info.fallback_reason = "Flash Attention not supported on MPS devices"
            logger.debug("Flash Attention not supported on MPS, will use PyTorch SDPA")
        
        elif device.type == "cpu":
            # Flash Attention is not supported on CPU
            device_info.supports_device = False
            device_info.fallback_reason = "Flash Attention not supported on CPU"
            logger.debug("Flash Attention not supported on CPU, will use PyTorch SDPA")
        
        else:
            device_info.supports_device = False
            device_info.fallback_reason = f"Unknown device type: {device.type}"
            logger.warning(f"Unknown device type for Flash Attention: {device.type}")
        
        self._flash_info_cache[device_key] = device_info
        return device_info
    
    def _is_version_compatible(self, version: str) -> bool:
        """Check if Flash Attention version is compatible."""
        try:
            # Parse version string (e.g., "2.6.3" -> (2, 6, 3))
            version_parts = [int(x) for x in version.split('.')]
            
            # Require version >= 2.5.7
            required = (2, 5, 7)
            
            for i, (current, req) in enumerate(zip(version_parts, required)):
                if current > req:
                    return True
                elif current < req:
                    return False
            
            # If we get here, versions are equal up to the compared parts
            return len(version_parts) >= len(required)
        
        except (ValueError, AttributeError):
            logger.warning(f"Could not parse Flash Attention version: {version}")
            return False
    
    def _check_cuda_compatibility(self, device: torch.device) -> bool:
        """Check CUDA device compatibility with Flash Attention."""
        try:
            if not torch.cuda.is_available():
                return False
            
            # Get device properties
            device_props = torch.cuda.get_device_properties(device)
            compute_capability = (device_props.major, device_props.minor)
            
            # Flash Attention requires compute capability >= 7.5
            min_capability = (7, 5)
            
            if (compute_capability[0] > min_capability[0] or 
                (compute_capability[0] == min_capability[0] and 
                 compute_capability[1] >= min_capability[1])):
                
                logger.debug(f"CUDA device {device} supports Flash Attention "
                           f"(CC {compute_capability})")
                return True
            else:
                logger.debug(f"CUDA device {device} does not support Flash Attention "
                           f"(CC {compute_capability} < {min_capability})")
                return False
        
        except Exception as e:
            logger.warning(f"Error checking CUDA compatibility: {e}")
            return False
    
    def get_attention_config(self, device: torch.device) -> Dict[str, Any]:
        """
        Get attention configuration for a device.
        
        Args:
            device: Target device
            
        Returns:
            Dictionary with attention configuration
        """
        flash_info = self.check_device_compatibility(device)
        
        config = {
            "use_flash_attention": flash_info.supports_device,
            "use_pytorch_sdpa": not flash_info.supports_device,
            "flash_attention_version": flash_info.version,
            "fallback_reason": flash_info.fallback_reason,
            "supports_rotary": flash_info.supports_rotary,
        }
        
        # Device-specific settings
        if device.type == "cuda" and flash_info.supports_device:
            config.update({
                "deterministic_fa2": False,  # Can be enabled for reproducibility
                "use_fa2": True,
            })
        else:
            config.update({
                "use_fa2": False,
                "use_sdpa_attn_mask": True,  # Use attention masks with SDPA
            })
        
        return config
    
    def log_attention_status(self, device: torch.device):
        """Log the attention implementation status for a device."""
        flash_info = self.check_device_compatibility(device)
        
        if flash_info.supports_device:
            logger.info(f"Using Flash Attention {flash_info.version} on {device}")
        else:
            logger.info(f"Using PyTorch SDPA on {device} "
                       f"(Flash Attention: {flash_info.fallback_reason})")
    
    def apply_attention_config(self, model_config, device: torch.device):
        """
        Apply attention configuration to model config based on device.
        
        Args:
            model_config: Model configuration object
            device: Target device
        """
        attention_config = self.get_attention_config(device)
        
        # Apply configuration to model config
        if hasattr(model_config, 'use_fa2'):
            model_config.use_fa2 = attention_config["use_flash_attention"]
        
        if hasattr(model_config, 'deterministic_fa2'):
            model_config.deterministic_fa2 = attention_config.get("deterministic_fa2", False)
        
        if hasattr(model_config, 'use_sdpa_attn_mask'):
            model_config.use_sdpa_attn_mask = attention_config["use_pytorch_sdpa"]
        
        logger.debug(f"Applied attention config for {device}: {attention_config}")


# Global Flash Attention manager instance
flash_attention_manager = FlashAttentionManager()
