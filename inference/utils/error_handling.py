# Copyright 2024 onwards Answer.AI, LightOn, and contributors
# License: Apache-2.0

"""
Comprehensive error handling and user feedback utilities for ModernBERT inference.

Provides clear error messages, warnings, and troubleshooting guidance for
device-related issues and other common problems.
"""

import torch
import logging
import traceback
from typing import Dict, Any, List, Optional, Callable
from dataclasses import dataclass
from enum import Enum

logger = logging.getLogger(__name__)


class ErrorSeverity(Enum):
    """Error severity levels."""
    INFO = "info"
    WARNING = "warning"
    ERROR = "error"
    CRITICAL = "critical"


@dataclass
class ErrorInfo:
    """Information about an error or issue."""
    severity: ErrorSeverity
    message: str
    details: Optional[str] = None
    troubleshooting: Optional[List[str]] = None
    error_code: Optional[str] = None


class DeviceErrorHandler:
    """
    Handles device-related errors and provides user-friendly feedback.
    """
    
    def __init__(self):
        """Initialize error handler."""
        self.error_history = []
    
    def handle_device_error(self, error: Exception, device: torch.device, 
                          context: str = "") -> ErrorInfo:
        """
        Handle device-related errors with comprehensive feedback.
        
        Args:
            error: The exception that occurred
            device: The device that caused the error
            context: Additional context about when the error occurred
            
        Returns:
            ErrorInfo with detailed error information and troubleshooting
        """
        error_type = type(error).__name__
        error_msg = str(error)
        
        # Determine error severity and create appropriate response
        if "CUDA" in error_msg or "cuda" in error_msg:
            return self._handle_cuda_error(error, device, context)
        elif "MPS" in error_msg or "mps" in error_msg:
            return self._handle_mps_error(error, device, context)
        elif "memory" in error_msg.lower() or "out of memory" in error_msg.lower():
            return self._handle_memory_error(error, device, context)
        elif "device" in error_msg.lower():
            return self._handle_generic_device_error(error, device, context)
        else:
            return self._handle_unknown_error(error, device, context)
    
    def _handle_cuda_error(self, error: Exception, device: torch.device, 
                          context: str) -> ErrorInfo:
        """Handle CUDA-specific errors."""
        error_msg = str(error)
        
        if "CUDA out of memory" in error_msg:
            return ErrorInfo(
                severity=ErrorSeverity.ERROR,
                message=f"CUDA out of memory on {device}",
                details=f"Error occurred during: {context}",
                troubleshooting=[
                    "Reduce batch size or sequence length",
                    "Use mixed precision (fp16/bf16) to reduce memory usage",
                    "Clear CUDA cache: torch.cuda.empty_cache()",
                    "Try using a smaller model or CPU inference",
                    "Close other GPU applications to free memory"
                ],
                error_code="CUDA_OOM"
            )
        
        elif "CUDA driver" in error_msg or "CUDA runtime" in error_msg:
            return ErrorInfo(
                severity=ErrorSeverity.CRITICAL,
                message="CUDA driver or runtime error",
                details=error_msg,
                troubleshooting=[
                    "Update CUDA drivers to the latest version",
                    "Verify CUDA installation: nvidia-smi",
                    "Check PyTorch CUDA compatibility",
                    "Restart the system if driver issues persist",
                    "Fall back to CPU inference if CUDA cannot be fixed"
                ],
                error_code="CUDA_DRIVER"
            )
        
        elif "device-side assert" in error_msg:
            return ErrorInfo(
                severity=ErrorSeverity.ERROR,
                message="CUDA device assertion failed",
                details=f"Device assertion during: {context}",
                troubleshooting=[
                    "Check input tensor shapes and values",
                    "Verify model configuration matches checkpoint",
                    "Try running with CUDA_LAUNCH_BLOCKING=1 for better error info",
                    "Use CPU inference to isolate the issue"
                ],
                error_code="CUDA_ASSERT"
            )
        
        else:
            return ErrorInfo(
                severity=ErrorSeverity.ERROR,
                message=f"CUDA error on {device}",
                details=error_msg,
                troubleshooting=[
                    "Check CUDA device availability: torch.cuda.is_available()",
                    "Verify device index is valid",
                    "Try falling back to CPU inference",
                    "Check system logs for hardware issues"
                ],
                error_code="CUDA_GENERIC"
            )
    
    def _handle_mps_error(self, error: Exception, device: torch.device, 
                         context: str) -> ErrorInfo:
        """Handle MPS-specific errors."""
        error_msg = str(error)
        
        if "MPS backend out of memory" in error_msg:
            return ErrorInfo(
                severity=ErrorSeverity.ERROR,
                message=f"MPS out of memory on {device}",
                details=f"Error occurred during: {context}",
                troubleshooting=[
                    "Reduce batch size or sequence length",
                    "Use mixed precision (fp16) to reduce memory usage",
                    "Close other applications using GPU memory",
                    "Try CPU inference if memory issues persist",
                    "Consider using a smaller model"
                ],
                error_code="MPS_OOM"
            )
        
        elif "operation not supported" in error_msg.lower():
            return ErrorInfo(
                severity=ErrorSeverity.WARNING,
                message="MPS operation not supported",
                details=error_msg,
                troubleshooting=[
                    "Some operations may not be supported on MPS",
                    "Try using CPU inference for unsupported operations",
                    "Check PyTorch MPS documentation for supported operations",
                    "Consider using CUDA if available"
                ],
                error_code="MPS_UNSUPPORTED"
            )
        
        else:
            return ErrorInfo(
                severity=ErrorSeverity.ERROR,
                message=f"MPS error on {device}",
                details=error_msg,
                troubleshooting=[
                    "Check MPS availability: torch.backends.mps.is_available()",
                    "Update to latest macOS and PyTorch versions",
                    "Try falling back to CPU inference",
                    "Check Apple Silicon compatibility"
                ],
                error_code="MPS_GENERIC"
            )
    
    def _handle_memory_error(self, error: Exception, device: torch.device, 
                           context: str) -> ErrorInfo:
        """Handle memory-related errors."""
        return ErrorInfo(
            severity=ErrorSeverity.ERROR,
            message=f"Memory error on {device}",
            details=f"Error during: {context}",
            troubleshooting=[
                "Reduce batch size or sequence length",
                "Use mixed precision to reduce memory usage",
                "Clear device cache if using CUDA",
                "Close other memory-intensive applications",
                "Try using CPU inference",
                "Consider using gradient checkpointing"
            ],
            error_code="MEMORY_ERROR"
        )
    
    def _handle_generic_device_error(self, error: Exception, device: torch.device, 
                                   context: str) -> ErrorInfo:
        """Handle generic device errors."""
        return ErrorInfo(
            severity=ErrorSeverity.ERROR,
            message=f"Device error on {device}",
            details=str(error),
            troubleshooting=[
                "Verify device is available and accessible",
                "Check device compatibility with PyTorch",
                "Try using a different device (CPU/CUDA/MPS)",
                "Restart the Python process",
                "Check system logs for hardware issues"
            ],
            error_code="DEVICE_GENERIC"
        )
    
    def _handle_unknown_error(self, error: Exception, device: torch.device, 
                            context: str) -> ErrorInfo:
        """Handle unknown errors."""
        return ErrorInfo(
            severity=ErrorSeverity.ERROR,
            message=f"Unknown error on {device}",
            details=f"{type(error).__name__}: {str(error)}",
            troubleshooting=[
                "Check the full error traceback for more details",
                "Try using CPU inference to isolate device issues",
                "Verify all dependencies are correctly installed",
                "Check for known issues in the project documentation"
            ],
            error_code="UNKNOWN_ERROR"
        )
    
    def log_error_info(self, error_info: ErrorInfo):
        """Log error information with appropriate severity."""
        message = f"{error_info.message}"
        if error_info.details:
            message += f" - {error_info.details}"
        
        if error_info.severity == ErrorSeverity.INFO:
            logger.info(message)
        elif error_info.severity == ErrorSeverity.WARNING:
            logger.warning(message)
        elif error_info.severity == ErrorSeverity.ERROR:
            logger.error(message)
        elif error_info.severity == ErrorSeverity.CRITICAL:
            logger.critical(message)
        
        # Log troubleshooting steps
        if error_info.troubleshooting:
            logger.info("Troubleshooting suggestions:")
            for i, step in enumerate(error_info.troubleshooting, 1):
                logger.info(f"  {i}. {step}")
    
    def get_memory_usage_info(self, device: torch.device) -> Dict[str, Any]:
        """Get memory usage information for troubleshooting."""
        info = {"device": str(device)}
        
        try:
            if device.type == "cuda":
                torch.cuda.set_device(device)
                info.update({
                    "total_memory": torch.cuda.get_device_properties(device).total_memory,
                    "allocated_memory": torch.cuda.memory_allocated(device),
                    "cached_memory": torch.cuda.memory_reserved(device),
                    "available_memory": (torch.cuda.get_device_properties(device).total_memory - 
                                       torch.cuda.memory_allocated(device))
                })
            else:
                info["memory_info"] = "Memory info not available for this device type"
        
        except Exception as e:
            info["memory_error"] = str(e)
        
        return info
    
    def suggest_device_fallback(self, failed_device: torch.device) -> List[str]:
        """Suggest alternative devices when one fails."""
        suggestions = []
        
        if failed_device.type == "cuda":
            if hasattr(torch.backends, 'mps') and torch.backends.mps.is_available():
                suggestions.append("Try using MPS device: device='mps'")
            suggestions.append("Fall back to CPU: device='cpu'")
        
        elif failed_device.type == "mps":
            if torch.cuda.is_available():
                suggestions.append("Try using CUDA device: device='cuda'")
            suggestions.append("Fall back to CPU: device='cpu'")
        
        elif failed_device.type == "cpu":
            if torch.cuda.is_available():
                suggestions.append("Try using CUDA device: device='cuda'")
            if hasattr(torch.backends, 'mps') and torch.backends.mps.is_available():
                suggestions.append("Try using MPS device: device='mps'")
        
        return suggestions


# Global error handler instance
device_error_handler = DeviceErrorHandler()
