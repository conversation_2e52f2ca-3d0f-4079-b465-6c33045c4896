# Copyright 2024 onwards Answer.AI, LightOn, and contributors
# License: Apache-2.0

from typing import Dict, Any, Optional, Union, Literal
from dataclasses import dataclass
import torch
import logging

# Handle imports - try relative first, then absolute
try:
    from ..utils.device_utils import device_manager, DeviceInfo
except ImportError:
    # If relative imports fail, try direct module imports
    import sys
    from pathlib import Path
    inference_dir = Path(__file__).parent.parent
    sys.path.insert(0, str(inference_dir))

    from utils.device_utils import device_manager, DeviceInfo

logger = logging.getLogger(__name__)


@dataclass
class InferenceConfig:
    """
    Configuration for inference-specific settings.

    These settings control how inference is performed but don't affect
    the model architecture itself.
    """

    # Device and precision settings
    device: str = "auto"  # "auto", "cpu", "cuda", "cuda:0", "mps", etc.
    precision: Literal["fp32", "fp16", "bf16"] = "fp32"

    # Device-related internal state (set during validation)
    _selected_device: Optional[torch.device] = None
    _device_info: Optional[DeviceInfo] = None
    
    # Batch processing settings
    max_batch_size: int = 32
    max_sequence_length: int = 512
    
    # Model behavior settings
    use_cache: bool = False  # Whether to use KV caching (typically for generation)
    
    # MLM-specific settings
    mlm_top_k: int = 10
    mlm_top_p: float = 0.9
    mlm_temperature: float = 1.0
    mlm_return_all_predictions: bool = False
    
    # Embedding-specific settings
    embedding_pooling_strategy: Literal["mean", "cls", "max", "mean_sqrt_len"] = "mean"
    embedding_normalize: bool = True
    embedding_layer: int = -1  # -1 for last layer, 0-based indexing
    
    # Performance settings
    use_torch_compile: bool = False
    enable_memory_efficient_attention: bool = True
    gradient_checkpointing: bool = False
    
    # Output settings
    return_attention_weights: bool = False
    return_hidden_states: bool = False
    
    def __post_init__(self):
        """Validate and normalize configuration after initialization."""
        self._validate_device()
        self._validate_precision()
        self._validate_ranges()

    def _validate_device(self):
        """Validate and normalize device setting with comprehensive device detection."""
        try:
            # Use device manager for robust device selection
            selected_device, device_info = device_manager.select_best_device(self.device)

            self._selected_device = selected_device
            self._device_info = device_info

            # Update device string to match selected device
            if self.device == "auto":
                self.device = str(selected_device)
                logger.info(f"Auto-selected device: {device_info.device_name} ({selected_device})")
            else:
                logger.info(f"Using specified device: {device_info.device_name} ({selected_device})")

            # Validate device compatibility
            is_compatible, warnings = device_manager.validate_device_compatibility(
                device_info,
                requires_flash_attention=True,  # ModernBERT uses rotary embeddings
                requires_mixed_precision=(self.precision in ["fp16", "bf16"])
            )

            # Log warnings
            for warning in warnings:
                logger.warning(warning)

        except ValueError as e:
            logger.error(f"Device validation failed: {e}")
            # Fallback to CPU
            logger.warning("Falling back to CPU device")
            self.device = "cpu"
            self._selected_device = torch.device("cpu")
            # Get CPU device info
            devices = device_manager.detect_available_devices()
            self._device_info = next(d for d in devices if d.device_type == "cpu")
        except Exception as e:
            logger.error(f"Unexpected error during device validation: {e}")
            # Safe fallback
            self.device = "cpu"
            self._selected_device = torch.device("cpu")
            self._device_info = None
    
    def _validate_precision(self):
        """Validate precision setting based on device capabilities."""
        if self._device_info is None:
            logger.warning("Device info not available, using conservative precision settings")
            if self.precision in ["fp16", "bf16"]:
                self.precision = "fp32"
            return

        device_type = self._device_info.device_type

        # Device-specific precision validation
        if device_type == "cpu":
            if self.precision in ["fp16", "bf16"]:
                logger.warning("Mixed precision not recommended on CPU, using FP32")
                self.precision = "fp32"

        elif device_type == "cuda":
            # Check BF16 support on CUDA
            if self.precision == "bf16":
                if not torch.cuda.is_bf16_supported():
                    logger.warning("BF16 not supported on this CUDA device, falling back to FP16")
                    self.precision = "fp16"
                elif self._device_info.compute_capability and self._device_info.compute_capability < (8, 0):
                    logger.warning("BF16 not optimal on older CUDA devices, consider using FP16")

        elif device_type == "mps":
            # MPS has limited mixed precision support
            if self.precision == "bf16":
                logger.warning("BF16 not well supported on MPS, falling back to FP16")
                self.precision = "fp16"
            elif self.precision == "fp16":
                logger.info("Using FP16 on MPS device")

        logger.debug(f"Final precision setting: {self.precision} for {device_type} device")
    
    def _validate_ranges(self):
        """Validate numeric ranges."""
        if self.max_batch_size <= 0:
            raise ValueError("max_batch_size must be positive")
        
        if self.max_sequence_length <= 0:
            raise ValueError("max_sequence_length must be positive")
        
        if not 0 < self.mlm_top_p <= 1.0:
            raise ValueError("mlm_top_p must be between 0 and 1")
        
        if self.mlm_temperature <= 0:
            raise ValueError("mlm_temperature must be positive")
        
        if self.mlm_top_k <= 0:
            raise ValueError("mlm_top_k must be positive")
    
    @property
    def torch_dtype(self) -> torch.dtype:
        """Get the corresponding PyTorch dtype."""
        if self.precision == "fp16":
            return torch.float16
        elif self.precision == "bf16":
            return torch.bfloat16
        else:
            return torch.float32
    
    @property
    def is_cuda(self) -> bool:
        """Check if using CUDA device."""
        return self._selected_device is not None and self._selected_device.type == "cuda"

    @property
    def is_mps(self) -> bool:
        """Check if using MPS device."""
        return self._selected_device is not None and self._selected_device.type == "mps"

    @property
    def is_cpu(self) -> bool:
        """Check if using CPU device."""
        return self._selected_device is not None and self._selected_device.type == "cpu"

    @property
    def is_mixed_precision(self) -> bool:
        """Check if using mixed precision."""
        return self.precision in ["fp16", "bf16"]

    @property
    def selected_device(self) -> Optional[torch.device]:
        """Get the selected torch device."""
        return self._selected_device

    @property
    def device_info(self) -> Optional[DeviceInfo]:
        """Get detailed device information."""
        return self._device_info

    @property
    def supports_flash_attention(self) -> bool:
        """Check if the selected device supports Flash Attention."""
        return self._device_info is not None and self._device_info.supports_flash_attention
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        result = {
            "device": self.device,
            "precision": self.precision,
            "torch_dtype": str(self.torch_dtype),
            "max_batch_size": self.max_batch_size,
            "max_sequence_length": self.max_sequence_length,
            "use_cache": self.use_cache,
            "mlm_top_k": self.mlm_top_k,
            "mlm_top_p": self.mlm_top_p,
            "mlm_temperature": self.mlm_temperature,
            "mlm_return_all_predictions": self.mlm_return_all_predictions,
            "embedding_pooling_strategy": self.embedding_pooling_strategy,
            "embedding_normalize": self.embedding_normalize,
            "embedding_layer": self.embedding_layer,
            "use_torch_compile": self.use_torch_compile,
            "enable_memory_efficient_attention": self.enable_memory_efficient_attention,
            "gradient_checkpointing": self.gradient_checkpointing,
            "return_attention_weights": self.return_attention_weights,
            "return_hidden_states": self.return_hidden_states,
        }

        # Add device information if available
        if self._selected_device is not None:
            result["selected_device"] = str(self._selected_device)

        if self._device_info is not None:
            result["device_info"] = {
                "device_type": self._device_info.device_type,
                "device_name": self._device_info.device_name,
                "supports_flash_attention": self._device_info.supports_flash_attention,
                "supports_mixed_precision": self._device_info.supports_mixed_precision,
            }

        return result
    
    @classmethod
    def from_dict(cls, config_dict: Dict[str, Any]) -> "InferenceConfig":
        """Create from dictionary."""
        # Filter out fields that aren't part of the dataclass
        valid_fields = {field.name for field in cls.__dataclass_fields__.values()}
        filtered_dict = {k: v for k, v in config_dict.items() if k in valid_fields}
        return cls(**filtered_dict)
