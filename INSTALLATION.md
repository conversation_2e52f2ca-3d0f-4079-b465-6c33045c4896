# ModernBERT Inference Installation Guide

This guide provides step-by-step instructions for installing all dependencies required for the ModernBERT inference pipeline.

## Prerequisites

- Python 3.8 or higher (Python 3.11 recommended)
- CUDA-compatible GPU (recommended) or CPU
- **Conda package manager (strongly recommended)**
- Git for cloning repositories

## Recommended Installation (Conda Environment)

**We strongly recommend using conda to manage your environment** as it provides better dependency isolation and easier management of CUDA/PyTorch compatibility.

### Step 1: Create and Activate Conda Environment

Create a dedicated conda environment for ModernBERT inference:

```bash
# Create new environment with Python 3.11
conda create -n modernbert-inference python=3.11 -y

# Activate the environment
conda activate modernbert-inference
```

### Step 2: Install PyTorch with CUDA Support (Recommended)

Install PyTorch with CUDA support for optimal performance:

```bash
# For CUDA 11.8 (most common)
conda install pytorch torchvision torchaudio pytorch-cuda=11.8 -c pytorch -c nvidia -y

# For CUDA 12.1 (if you have newer drivers)
conda install pytorch torchvision torchaudio pytorch-cuda=12.1 -c pytorch -c nvidia -y

# For CPU-only (if no GPU available)
conda install pytorch torchvision torchaudio cpuonly -c pytorch -y
```

### Step 3: Install Standard Dependencies

Install the remaining Python packages from requirements.txt:

```bash
pip install -r requirements.txt
```

This will install:
- transformers>=4.40.2
- omegaconf>=2.3.0
- einops>=0.8.0
- numpy
- mosaicml==0.24.1
- mosaicml-cli==0.6.41

### Step 4: Install Flash Attention (Required)

Flash Attention is required for the rotary embeddings functionality. It must be installed separately with a special flag:

```bash
pip install "flash_attn==2.6.3" --no-build-isolation
```

**Important Notes:**
- The `--no-build-isolation` flag is required for flash_attn installation
- This step may take several minutes as flash_attn compiles from source
- Ensure you have a CUDA-compatible environment if using GPU
- If compilation fails, see the troubleshooting section below

### Step 5: Verify Installation

Test that all dependencies are properly installed:

```bash
python -c "
import torch
import transformers
import omegaconf
import einops
import numpy
print(f'✓ PyTorch version: {torch.__version__}')
print(f'✓ CUDA available: {torch.cuda.is_available()}')
if torch.cuda.is_available():
    print(f'✓ CUDA version: {torch.version.cuda}')
    print(f'✓ GPU device: {torch.cuda.get_device_name(0)}')
try:
    import flash_attn
    print(f'✓ Flash Attention version: {flash_attn.__version__}')
    print('✓ All dependencies installed successfully!')
except ImportError as e:
    print(f'✗ Flash Attention import failed: {e}')
"
```

### Step 6: Test the Inference Pipeline

Run the working example to verify everything is working:

```bash
python inference/working_example.py
```

## Alternative Installation (pip only)

If you prefer not to use conda or need to use an existing Python environment:

### Step 1: Create Virtual Environment (Optional but Recommended)

```bash
# Create virtual environment
python -m venv modernbert-env

# Activate environment (Linux/macOS)
source modernbert-env/bin/activate

# Activate environment (Windows)
modernbert-env\Scripts\activate
```

### Step 2: Install Dependencies

```bash
# Install all dependencies from requirements.txt
pip install -r requirements.txt

# Install Flash Attention
pip install "flash_attn==2.6.3" --no-build-isolation
```

## Troubleshooting

### Flash Attention Installation Issues

If flash_attn installation fails:

1. **CUDA Compatibility**: Ensure your CUDA version is compatible with PyTorch:
   ```bash
   # Check CUDA version
   nvcc --version
   # Check PyTorch CUDA version
   python -c "import torch; print(torch.version.cuda)"
   ```

2. **Compilation Errors**: Try installing with verbose output:
   ```bash
   pip install "flash_attn==2.6.3" --no-build-isolation -v
   ```

3. **Memory Issues**: Close other applications to free up system memory during compilation

4. **Conda Environment Issues**: Make sure you're in the correct conda environment:
   ```bash
   conda activate modernbert-inference
   which python  # Should point to your conda environment
   ```

5. **Alternative Flash Attention Installation**: If the above fails, try:
   ```bash
   # Install build dependencies first
   conda install ninja -y
   pip install packaging
   pip install "flash_attn==2.6.3" --no-build-isolation
   ```

### Environment Management

**Always activate your conda environment before working:**
```bash
conda activate modernbert-inference
```

**To deactivate when done:**
```bash
conda deactivate
```

**To remove the environment if needed:**
```bash
conda env remove -n modernbert-inference
```

### Import Errors

If you get import errors when running the inference:

1. **Environment**: Make sure you're in the correct conda environment:
   ```bash
   conda activate modernbert-inference
   ```

2. **Path Issues**: Ensure you're running from the project root directory

3. **Missing Dependencies**: Re-run the installation steps above

4. **CUDA/PyTorch Mismatch**: Reinstall PyTorch with correct CUDA version

## Dependency Details

### Core Dependencies
- **torch**: PyTorch deep learning framework
- **transformers**: Hugging Face transformers library
- **omegaconf**: Configuration management
- **einops**: Tensor operations
- **numpy**: Numerical computing

### MosaicML Dependencies
- **mosaicml**: Core MosaicML training utilities
- **mosaicml-cli**: Command-line interface tools

### Attention Dependencies
- **flash_attn**: Optimized attention implementation with rotary embeddings

## Environment Management Best Practices

### Daily Usage

Always activate your conda environment before working:
```bash
conda activate modernbert-inference
```

### Updating Dependencies

To update packages in your conda environment:
```bash
conda activate modernbert-inference
pip install --upgrade -r requirements.txt
```

### Environment Export/Import

To share your environment or backup:
```bash
# Export environment
conda env export > environment.yml

# Create environment from file
conda env create -f environment.yml
```

## Memory Requirements

- **Minimum**: 8GB RAM, 4GB GPU memory
- **Recommended**: 16GB RAM, 8GB+ GPU memory
- **Model Loading**: Additional memory based on model size
- **Flash Attention Compilation**: 4GB+ free RAM during installation

## Next Steps

After successful installation:

1. **Always activate your conda environment**: `conda activate modernbert-inference`
2. Update checkpoint paths in `inference/working_example.py`
3. Run inference examples in `inference/examples/`
4. See `inference/README.md` for detailed usage instructions

## Support

If you encounter issues:

1. **Check environment**: Ensure you're in the correct conda environment
2. **Check this troubleshooting section** for common solutions
3. **Verify CUDA/PyTorch compatibility** using the verification commands above
4. **Ensure all installation steps were completed successfully**
5. Check the original ModernBERT repository for additional guidance

## Why We Recommend Conda

- **Better dependency management**: Conda handles complex dependencies more reliably
- **CUDA compatibility**: Easier to install PyTorch with correct CUDA versions
- **Environment isolation**: Prevents conflicts with other Python projects
- **Reproducibility**: Easy to share and recreate environments
- **Package availability**: Many scientific packages are optimized for conda
