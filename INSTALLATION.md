# ModernBERT Inference Installation Guide

This guide provides step-by-step instructions for installing all dependencies required for the ModernBERT inference pipeline.

## Prerequisites

- Python 3.8 or higher
- CUDA-compatible GPU (recommended) or CPU
- Conda or pip package manager

## Installation Steps

### Step 1: Install Standard Dependencies

First, install the standard Python packages from requirements.txt:

```bash
pip install -r requirements.txt
```

This will install:
- torch>=2.3.0
- transformers>=4.40.2
- omegaconf>=2.3.0
- einops>=0.8.0
- numpy
- mosaicml==0.24.1
- mosaicml-cli==0.6.41

### Step 2: Install Flash Attention (Required)

Flash Attention is required for the rotary embeddings functionality. It must be installed separately with a special flag:

```bash
pip install "flash_attn==2.6.3" --no-build-isolation
```

**Important Notes:**
- The `--no-build-isolation` flag is required for flash_attn installation
- This step may take several minutes as flash_attn compiles from source
- Ensure you have a CUDA-compatible environment if using GPU

### Step 3: Verify Installation

Test that all dependencies are properly installed:

```bash
python -c "
import torch
import transformers
import omegaconf
import einops
import numpy
try:
    import flash_attn
    print('✓ All dependencies installed successfully!')
    print(f'Flash Attention version: {flash_attn.__version__}')
except ImportError as e:
    print(f'✗ Flash Attention import failed: {e}')
"
```

### Step 4: Test the Inference Pipeline

Run the working example to verify everything is working:

```bash
python inference/working_example.py
```

## Troubleshooting

### Flash Attention Installation Issues

If flash_attn installation fails:

1. **CUDA Issues**: Ensure you have a compatible CUDA version installed
2. **Compilation Errors**: Try installing with verbose output:
   ```bash
   pip install "flash_attn==2.6.3" --no-build-isolation -v
   ```
3. **Memory Issues**: Close other applications to free up system memory during compilation

### Import Errors

If you get import errors when running the inference:

1. **Environment**: Make sure you're in the correct conda environment
2. **Path Issues**: Ensure you're running from the project root directory
3. **Missing Dependencies**: Re-run the installation steps above

### Alternative Installation (Conda Environment)

If you prefer using conda, you can create a dedicated environment:

```bash
# Create new environment
conda create -n modernbert-inference python=3.11

# Activate environment
conda activate modernbert-inference

# Install dependencies
pip install -r requirements.txt
pip install "flash_attn==2.6.3" --no-build-isolation
```

## Dependency Details

### Core Dependencies
- **torch**: PyTorch deep learning framework
- **transformers**: Hugging Face transformers library
- **omegaconf**: Configuration management
- **einops**: Tensor operations
- **numpy**: Numerical computing

### MosaicML Dependencies
- **mosaicml**: Core MosaicML training utilities
- **mosaicml-cli**: Command-line interface tools

### Attention Dependencies
- **flash_attn**: Optimized attention implementation with rotary embeddings

## Memory Requirements

- **Minimum**: 8GB RAM, 4GB GPU memory
- **Recommended**: 16GB RAM, 8GB+ GPU memory
- **Model Loading**: Additional memory based on model size

## Next Steps

After successful installation:

1. Update checkpoint paths in `inference/working_example.py`
2. Run inference examples in `inference/examples/`
3. See `inference/README.md` for detailed usage instructions

## Support

If you encounter issues:

1. Check this troubleshooting section
2. Verify your CUDA/PyTorch compatibility
3. Ensure all installation steps were completed successfully
4. Check the original ModernBERT repository for additional guidance
