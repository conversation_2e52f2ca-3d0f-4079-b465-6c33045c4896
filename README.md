# ModernBERT Inference

A minimal, focused inference pipeline for ModernBERT models, extracted from the larger ModernBERT training repository.

## Features

- **MLM Inference**: Masked Language Modeling with top-k predictions
- **Embedding Generation**: Sentence embeddings with multiple pooling strategies  
- **Clean Dependencies**: Minimal requirements focused only on inference
- **Easy Integration**: Simple API compatible with Hugging Face patterns

## Quick Start

### 1. Installation

⚠️ **Important**: Flash Attention requires special installation steps.

```bash
# Install standard dependencies
pip install -r requirements.txt

# Install Flash Attention separately (required for rotary embeddings)
pip install "flash_attn==2.6.3" --no-build-isolation
```

For detailed installation instructions and troubleshooting, see [INSTALLATION.md](INSTALLATION.md).

### 2. Basic Usage

```python
from inference import ModernBERTInference

# Initialize inference system
with ModernBERTInference(
    config_path="path/to/config.yaml",
    checkpoint_path="path/to/checkpoint.pt"
) as inference:
    
    # MLM inference
    predictions = inference.predict_masked_tokens(
        "The capital of France is <mask>."
    )
    
    # Embedding generation
    embeddings = inference.encode_texts([
        "This is a sentence.",
        "This is another sentence."
    ])
```

### 3. Run Example

```bash
# Update paths in the example file first
python inference/working_example.py
```

## Project Structure

```
ModernBert_inference/
├── README.md                   # This file
├── INSTALLATION.md             # Detailed installation guide
├── requirements.txt            # Standard Python dependencies
├── inference/                  # Main inference package
│   ├── inference.py           # Main inference interface
│   ├── config/                # Configuration management
│   ├── core/                  # Core infrastructure
│   ├── pipelines/             # Task-specific pipelines
│   ├── examples/              # Usage examples
│   └── README.md              # Detailed inference documentation
├── src/                       # Model implementation
│   ├── bert_layers/           # FlexBERT model layers
│   └── bert_padding.py        # Padding utilities
└── yamls/                     # Configuration files
```

## Documentation

- **[INSTALLATION.md](INSTALLATION.md)**: Complete installation guide with troubleshooting
- **[inference/README.md](inference/README.md)**: Detailed inference API documentation
- **[inference/QUICKSTART.md](inference/QUICKSTART.md)**: Quick start guide

## Requirements

### System Requirements
- Python 3.8+
- CUDA-compatible GPU (recommended) or CPU
- 8GB+ RAM, 4GB+ GPU memory

### Key Dependencies
- PyTorch >= 2.3.0
- Transformers >= 4.40.2
- Flash Attention == 2.6.3 (installed separately)
- OmegaConf >= 2.3.0
- einops >= 0.8.0

## Environment Setup

### Option 1: Use Existing Environment
```bash
pip install -r requirements.txt
pip install "flash_attn==2.6.3" --no-build-isolation
```

### Option 2: Create New Conda Environment
```bash
conda create -n modernbert-inference python=3.11
conda activate modernbert-inference
pip install -r requirements.txt
pip install "flash_attn==2.6.3" --no-build-isolation
```

## Examples

See the `inference/examples/` directory for:
- **MLM inference**: Masked language modeling examples
- **Embedding generation**: Sentence embedding examples  
- **Batch processing**: Efficient batch inference

## Troubleshooting

### Common Issues

1. **"rotary_emb is not installed"**: Install flash_attn with the correct command
2. **Import errors**: Ensure you're in the correct environment and running from project root
3. **CUDA issues**: Verify CUDA compatibility with your PyTorch installation

See [INSTALLATION.md](INSTALLATION.md) for detailed troubleshooting.

## License

Apache-2.0 License (same as original ModernBERT repository)

## Acknowledgments

This inference pipeline is extracted from the original ModernBERT repository by Answer.AI and LightOn. The core model implementation and training code remain unchanged, with this package focusing specifically on inference capabilities.
