# ModernBERT Inference Project .gitignore
# =====================================

# Python-specific ignores
# ------------------------
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Virtual environments
# --------------------
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/
.conda/
conda-meta/

# PyInstaller
# -----------
*.manifest
*.spec

# Unit test / coverage reports
# ----------------------------
htmlcov/
.tox/
.nox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
*.py,cover
.hypothesis/
.pytest_cache/
cover/

# Jupyter Notebook specific
# -------------------------
.ipynb_checkpoints/
*/.ipynb_checkpoints/*
# Remove outputs from committed notebooks
*.ipynb

# IPython
# -------
profile_default/
ipython_config.py

# Machine Learning specific files
# -------------------------------
# Model weights and checkpoints
*.pt
*.pth
*.bin
*.safetensors
*.ckpt
*.h5
*.hdf5
*.pkl
*.pickle
*.joblib

# Checkpoint directories (from YAML configs)
ckpt/
checkpoints/
checkpoint/
models/
model_cache/
saved_models/

# Dataset directories
data/
datasets/
dataset/
raw_data/
processed_data/
# Project-specific data paths (from YAML configs)
fineweb-edu-mds-split-copy/
my-copy-c4/

# Training and experiment logs
logs/
log/
runs/
outputs/
results/
experiments/
tb_runs/
tensorboard_logs/

# Weights & Biases
wandb/
.wandb/

# MLflow
mlruns/
mlartifacts/

# DVC (Data Version Control)
.dvc/
*.dvc

# Hydra experiment outputs
outputs/
multirun/

# IDE and Editor files
# -------------------
# VSCode
.vscode/
.history/

# PyCharm
.idea/
*.iws
*.iml
*.ipr

# Vim
*.swp
*.swo
*~

# Emacs
*~
\#*\#
/.emacs.desktop
/.emacs.desktop.lock
*.elc
auto-save-list
tramp
.\#*

# Sublime Text
*.sublime-workspace
*.sublime-project

# System and temporary files
# --------------------------
# macOS
.DS_Store
.AppleDouble
.LSOverride
Icon
._*
.DocumentRevisions-V100
.fseventsd
.Spotlight-V100
.TemporaryItems
.Trashes
.VolumeIcon.icns
.com.apple.timemachine.donotpresent
.AppleDB
.AppleDesktop
Network Trash Folder
Temporary Items
.apdisk

# Windows
Thumbs.db
Thumbs.db:encryptable
ehthumbs.db
ehthumbs_vista.db
*.stackdump
[Dd]esktop.ini
$RECYCLE.BIN/
*.cab
*.msi
*.msix
*.msm
*.msp
*.lnk

# Linux
*~
.fuse_hidden*
.directory
.Trash-*
.nfs*

# Temporary files
*.tmp
*.temp
*.log
*.out
*.err
.cache/
.temp/
temp/
tmp/

# Configuration and secrets
# -------------------------
.env.local
.env.*.local
*.key
*.pem
*.p12
*.pfx
secrets.yaml
secrets.yml
config.local.*

# Project-specific ignores
# ------------------------
# Composer/MosaicML specific
composer_logs/
.composer/

# Flash Attention build artifacts
flash_attn/

# Large model files that shouldn't be committed
*.model
*.vocab
*.tokenizer

# Evaluation results
eval_results/
evaluation/
benchmarks/

# Generated documentation
docs/_build/
site/

# Backup files
*.bak
*.backup
*.orig

# Lock files (keep requirements.txt but ignore lock files)
poetry.lock
Pipfile.lock
conda-lock.yml

# OS generated files
.Spotlight-V100
.Trashes
