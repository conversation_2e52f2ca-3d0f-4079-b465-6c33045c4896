# ModernBERT Inference Dependencies
#
# INSTALLATION INSTRUCTIONS:
# 1. First install these standard dependencies:
#    pip install -r requirements.txt
#
# 2. Then install flash_attn separately (required for rotary embeddings):
#    pip install "flash_attn==2.6.3" --no-build-isolation
#
# Note: flash_attn requires the --no-build-isolation flag and cannot be
# included in this requirements.txt file.

torch>=2.3.0
transformers>=4.40.2
omegaconf>=2.3.0
einops>=0.8.0
numpy
mosaicml==0.24.1
mosaicml-cli==0.6.41